<template>
  <q-page padding>
    <div class="row items-center justify-between q-mb-md">
      <div class="body">การจัดการแบบสอบถาม</div>
      <div class="row items-center q-gutter-sm">
        <SearchBar />
        <q-btn
          label="สร้าง"
          icon="add"
          class="text-white"
          color="accent"
          s
          @click="onClickCreate"
        />
      </div>
    </div>
    <EvaluateTable />
  </q-page>
</template>

<script setup lang="ts">
import EvaluateTable from 'src/components/evaluate/EvaluateTable.vue';
import SearchBar from 'src/components/SearchBar.vue';
import router from 'src/router';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { api } from 'src/boot/axios';
import type { ItemBlock } from 'src/types/models';

async function onClickCreate() {
  try {
    const user = useAuthStore().getCurrentUser();

    console.log('=== Starting Evaluation Creation Flow ===');

    // Step 1: Create Assessment
    console.log('Step 1: Creating Assessment...');
    const assessmentResponse = await new AssessmentService('evaluate').createOne({
      creatorUserId: user?.id || 1,
      programId: 1,
      type: 'EVALUATE',
    });

    console.log('Assessment created:', JSON.stringify(assessmentResponse, null, 2));

    // Step 2: Try to Create Header Block (Expected to fail due to backend bug)
    console.log('Step 2: Attempting Header ItemBlock creation...');
    console.log('⚠️  Known Issue: Backend creates HeaderBody without required title field');

    let headerCreated = false;
    try {
      const headerBlockResponse = await api.post<ItemBlock>('/item-blocks/block', {
        assessmentId: assessmentResponse.id,
        type: 'HEADER',
        sequence: 1,
      });
      console.log(
        '✅ Header ItemBlock created:',
        JSON.stringify(headerBlockResponse.data, null, 2),
      );
      headerCreated = true;
    } catch (headerError) {
      console.error('❌ Header ItemBlock creation failed (expected due to backend bug)');
      console.error('Backend tries to create HeaderBody without title field');
      console.error('Header error details:', headerError);
    }

    // Step 3: Create Radio ItemBlock (Should work)
    console.log('Step 3: Creating Radio ItemBlock...');
    const radioBlockResponse = await api.post<ItemBlock>('/item-blocks/block', {
      assessmentId: assessmentResponse.id,
      type: 'RADIO',
      sequence: headerCreated ? 2 : 1,
    });

    console.log('✅ Radio ItemBlock created:', JSON.stringify(radioBlockResponse.data, null, 2));

    console.log('=== Evaluation Creation Flow Completed ===');
    console.log(`✅ Assessment created with ID: ${assessmentResponse.id}`);
    console.log(
      `${headerCreated ? '✅' : '❌'} Header block: ${headerCreated ? 'Created' : 'Failed (backend bug)'}`,
    );
    console.log('✅ Radio block: Created (with auto-generated question and options)');

    // Store the created assessment immediately in the evaluate form store
    evaluateFormStore.currentAssessment = assessmentResponse;

    // CRITICAL: Fetch the complete assessment data including itemBlocks before navigation
    console.log('Fetching complete assessment data to ensure itemBlocks are loaded...');
    try {
      await evaluateFormStore.fetchAssessmentById(assessmentResponse.id);
      console.log(
        '✅ Assessment data refreshed with itemBlocks:',
        evaluateFormStore.currentAssessment?.itemBlocks?.length || 0,
        'blocks',
      );

      // Validate that we have itemBlocks before proceeding
      if (
        !evaluateFormStore.currentAssessment?.itemBlocks ||
        evaluateFormStore.currentAssessment.itemBlocks.length === 0
      ) {
        console.error(
          '❌ No itemBlocks found after refresh - this will cause BlockCreator validation to fail',
        );
        console.error('Current assessment:', evaluateFormStore.currentAssessment);
      } else {
        console.log('✅ ItemBlocks validation passed - BlockCreator should work correctly');
      }
    } catch (fetchError) {
      console.error('❌ Failed to fetch complete assessment data:', fetchError);
      console.error('This may cause BlockCreator validation issues');
    }

    await router.push({
      name: 'evaluate-id',
      query: { mode: 'edit' },
      params: { id: assessmentResponse.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
  }
}
</script>

<style scoped></style>

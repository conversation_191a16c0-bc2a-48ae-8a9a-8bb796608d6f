<template>
  <q-page class="q-pa-md">
    <div v-if="editFormData">
      <!-- แสดงเฉพาะตอน section 1 -->
      <UserTextBlock
        v-if="currentSection === 1"
        :title="editFormData.itemBlocks?.[0]?.headerBody?.title.trim() || 'ไม่มีข้อมูล'"
        :description="
          editFormData.itemBlocks?.[0]?.headerBody?.description?.trim() || 'ไม่มีข้อมูล'
        "
      />

      <!-- แสดงเฉพาะ evaluateItem ที่ section ตรงกับ currentSection -->
      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <!-- <UserQuestionBlock
          :id="evaluateItem.id"
          :item="editFormData"
          :category="evaluateItem.type"
          @update-answer="handleAnswer"
        /> -->
        <!-- <UserImageBlock
            v-if="evaluateItem.type === 'image'"
            :title="evaluateItem.imageBody.title"
            :image-url="evaluateItem.imageBody.imageUrl"
            @update:title="evaluateItem.imageBody.title = $event"
            @update:image-url="evaluateItem.imageBody.imageUrl = $event"
          /> -->
      </div>
    </div>

    <div class="row q-pa-md btn-footer">
      <div class="col">
        <MyButton label="ล้างแบบสอบถาม" class="btn-clear" @click="clearForm" />
      </div>
      <div class="col-auto q-pr-lg">
        <MyButton v-if="currentSection > 1" label="กลับไปหน้าก่อน" @click="previousSection" />
      </div>
      <MyButton
        v-if="maxSection !== undefined && currentSection < maxSection"
        label="หน้าต่อไป"
        @click="nextSection"
      />

      <div class="col-auto">
        <MyButton v-if="currentSection === maxSection" label="ส่งแบบสอบถาม" @click="submitForm" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
// import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
// import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import MyButton from 'src/components/common/MyButton.vue';
import { computed, onMounted, ref } from 'vue';
import type { Assessment } from 'src/types/models';
import { AssessmentService } from 'src/services/asm/assessmentService';

const props = defineProps<{
  id: number;
}>();

const editFormData = ref<Assessment>();

onMounted(async () => {
  const res = await new AssessmentService('evaluate').fetchOne(props.id);
  editFormData.value = res;
});
const currentSection = ref(1);
const maxSection = computed(() => editFormData.value?.itemBlocks?.length);

// const answers = ref<{ [key: string]: any }>({});

// const handleAnswer = (answer: { type: string; value: any }) => {
//   answers.value[answer.type] = answer.value;
//   console.log('answers:', answers.value[answer.type]);
// };

const nextSection = () => {
  if (maxSection.value) {
    if (currentSection.value < maxSection.value) {
      currentSection.value++;
      console.log(currentSection);
    }
  }
};

const previousSection = () => {
  if (currentSection.value > 1) {
    currentSection.value--;
  }
};

const clearForm = () => {
  console.log('Form cleared');
};

const submitForm = () => {
  console.log('Submitting form');
};
</script>

<style scoped lang="scss">
.btn-footer {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  width: 100%;
}

.btn-clear {
  background-color: white;
  color: $primary;
}
</style>

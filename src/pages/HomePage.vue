<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import type User from '../types/ums/user';
import { ref, onMounted, computed } from 'vue';
import HomeCard from 'src/components/HomeCard.vue';
import { useGlobalStore } from 'src/stores/global';
import type { System } from 'src/types/app';

const authStore = useAuthStore();
const globalStore = useGlobalStore();
const router = useRouter();
const user = ref<User>();

const systems = computed(() => globalStore.getAllSystems());

// ! pause for now
// const hideCard = (system: System) => {
//   return !user.value?.psnPermissions.some((perm) => system.perId.includes(perm.perId));
// };

const onClickCard = async (system: System) => {
  await router.push({
    path: system.sysUrl,
  });
};

onMounted(() => {
  user.value = authStore.getCurrentUser();
});
</script>

<template>
  <q-page padding>
    <div class="row q-col-gutter-md justify-start">
      <div v-for="system in systems" :key="system.sysId">
        <HomeCard :system="system" @clickCard="onClickCard" />
      </div>
    </div>
  </q-page>
</template>

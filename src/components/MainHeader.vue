<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import utilsConfigs from 'src/configs/utilsConfigs';
import type PsnUser from 'src/types/ums/user';
import { useGlobalStore } from 'src/stores/global';

const authStore = useAuthStore();
const route = useRoute();
const user = ref<PsnUser>();
const globalStore = useGlobalStore();
const router = useRouter();
const quizTitle = ref<string>('');

const headerTitle = computed(() => {
  // ถ้าอยู่ในหน้าแบบทดสอบที่มี ID
  if (route.name === 'quiz-id' && route.params.id) {
    const quizId = route.params.id as string;

    // ใช้ quizTitle.value แทน เพื่อให้ reactive
    if (quizTitle.value) {
      return quizTitle.value;
    }

    // fallback ถ้ายังไม่มี title
    return `แบบทดสอบ #${quizId}`;
  }

  // ถ้าอยู่ในส่วนของแบบทดสอบ
  if (route.path.includes('/quiz')) {
    return 'ระบบแบบทดสอบ';
  }

  // ถ้าอยู่ในส่วนของแบบสอบถาม
  if (route.path.includes('/evaluate')) {
    return 'ระบบแบบสอบถาม';
  }

  // ถ้าอยู่ในส่วนของ UMS
  if (route.path.includes('/ums')) {
    return 'จัดการสิทธิ์ในระบบ';
  }

  // หน้าหลักหรือหน้าอื่นๆ
  return 'ระบบจัดการ';
});

const isEditMode = computed(() => {
  return route.query.mode === 'edit';
});

// เพิ่มการตรวจสอบว่าอยู่ในหน้า evaluate หรือไม่
const isEvaluatePage = computed(() => {
  return route.path.includes('/evaluate');
});

const goToHome = async () => {
  await router.push('/');
};

// ฟังก์ชันสำหรับดึง quiz title
const fetchQuizTitle = (quizId: string) => {
  try {
    // ใช้ฟังก์ชันใหม่จาก globalStore ที่จัดการทุกอย่าง
    const title = globalStore.fetchAndSetQuizTitle(quizId);
    quizTitle.value = title;
  } catch (error) {
    console.error('Error fetching quiz title:', error);
    quizTitle.value = `แบบทดสอบ #${quizId}`;
  }
};

// ฟังก์ชันสำหรับ Evaluate Status Bar
const onLink = () => {
  console.log('Link clicked');
};

const onPreview = async () => {
  await router.push('/evaluate/management/preview-evaluate');
  console.log('Preview clicked');
};

const onPublish = () => {
  console.log('Publish clicked');
};

// Watch route changes เพื่อ update quiz title
watch(
  () => route.params.id,
  (newId) => {
    if (route.name === 'quiz-id' && newId) {
      fetchQuizTitle(newId as string);
    } else {
      quizTitle.value = '';
    }
  },
  { immediate: true }
);
const onImportFile = () => {
  // สร้าง input element แบบ hidden
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls,.csv'; // กำหนดประเภทไฟล์ที่รองรับ
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      handleFileImport(file);
    }
  };
  input.click();
};
const handleFileImport = (file: File) => {
  console.log('Importing file:', file.name);
  // ใส่ logic สำหรับการ import file ที่นี่
  // เช่น อัพโหลดไฟล์ไปยัง server หรือ parse ข้อมูล
};
onMounted(() => {
  user.value = authStore.getCurrentUser();

  if (route.name === 'quiz-id' && route.params.id) {
    fetchQuizTitle(route.params.id as string);
  }
});
</script>

<template>
  <q-header>
    <div class="header-bg">
      <div class="left-bg"></div>
      <div class="right-bg"></div>
    </div>

    <q-toolbar class="text-black justify-between">
      <!-- Left Section -->
      <q-toolbar-title class="flex items-center q-gutter-x-sm">
        <q-btn
          padding="xs"
          style="background: transparent"
          icon="menu"
          color="white"
          flat
          @click="globalStore.toggleLeftDrawer()"
        ></q-btn>
        <q-img src="/brands/brand-white.webp" alt="brand" width="40px" height="40px"></q-img>

        <div class="text-white text-h6 q-ml-md cursor-pointer header-title-hover"
          @click="goToHome">
          {{ headerTitle }}
        </div>

        <div v-if="isEditMode" class="saving-container">
          <div class="saving-box">
            <span class="saving-text">กำลังบันทึก</span>
            <span class="animated-ellipsis">
              <span class="ellipsis-dot">.</span>
              <span class="ellipsis-dot">.</span>
              <span class="ellipsis-dot">.</span>
            </span>
          </div>
        </div>


      </q-toolbar-title>

      <!-- Right Section -->
      <div class="row items-center q-gutter-sm">
        <!-- Evaluate Action Buttons - แสดงเฉพาะในหน้า evaluate -->
        <div v-if="isEvaluatePage && isEditMode" class="evaluate-buttons-container q-mr-md">
          <q-btn
            flat
            round
            icon="upload_file"
            size="md"
            class="text-white import-file-btn"
            @click="onImportFile"
          >
            <q-tooltip class="bg-dark">นำเข้าไฟล์</q-tooltip></q-btn>
          <q-btn
            flat
            round
            icon="link"
            size="md"
            class="text-white evaluate-icon-btn"
            @click="onLink"

          ><q-tooltip class="bg-dark">คัดลอกลิงค์</q-tooltip></q-btn>
          <q-btn
            flat
            round
            icon="visibility"
            size="md"
            class="text-white evaluate-icon-btn"
            @click="onPreview"

          ><q-tooltip class="bg-dark">ดูพรีวิว</q-tooltip></q-btn>
          <q-btn
            unelevated
            label="เผยแพร่"
            color="yellow-8"
            @click="onPublish"
            class="publish-btn-improved"
          ></q-btn>
        </div>

        <!-- User Section -->
        <div class="cursor-pointer row">
          <div class="text-white q-mr-sm q-my-auto text-caption">
            <div>Tralalero Tralala</div>
            <div>Super Admin</div>
          </div>
          <q-avatar size="40px" class="q-mx-xs">
            <img v-if="user" src="/mockup/avatar.webp" />
            <q-icon v-else name="account_circle" size="40px" />
          </q-avatar>

          <q-menu anchor="bottom right" self="top right">
            <q-card style="min-width: 200px">
              <q-list bordered separator>
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="account_circle" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ user?.psnFullname }}</q-item-label>
                    <q-item-label caption v-for="(sys, index) in utilsConfigs.systems" :key="index">
                      • {{ sys.sysNameTh }}
                    </q-item-label>
                  </q-item-section>
                </q-item>

                <q-item clickable v-ripple @click="authStore.logout()">
                  <q-item-section avatar>
                    <q-icon name="logout" color="red" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-red">ออกจากระบบ </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>
          </q-menu>
        </div>
      </div>
    </q-toolbar>
  </q-header>
</template>

<style scoped>
.header-title-hover {
  transition: opacity 0.2s ease;
  user-select: none;
}

.header-title-hover:hover {
  opacity: 0.8;
  cursor: pointer;
}

.saving-container {
  position: relative;
  margin-left: 12px;
}

.saving-box {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.saving-text {
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1;
}

.animated-ellipsis {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}

.animated-ellipsis .ellipsis-dot {
  color: white;
  font-size: 0.75em;
  font-weight: bold;
  opacity: 0.8;
  animation-name: dot-wave-animation;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}

.animated-ellipsis .ellipsis-dot:nth-child(1) {
  animation-delay: 0s;
}
.animated-ellipsis .ellipsis-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.animated-ellipsis .ellipsis-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-wave-animation {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* Styles สำหรับ Evaluate Buttons */
.evaluate-buttons-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;
}

.evaluate-icon-btn {
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.evaluate-icon-btn .q-btn__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.evaluate-icon-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.publish-btn-improved {
  height: 40px !important;
  min-width: 100px;
  padding: 0 20px;
  color: black !important;
  font-weight: 500;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.publish-btn-improved .q-btn__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.publish-btn-improved:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .q-toolbar {
    flex-wrap: wrap;
  }

  .q-toolbar-title {
    width: 100%;
    margin-bottom: 8px;
  }

  .evaluate-buttons-container {
    gap: 6px;
  }

  .evaluate-icon-btn {
    width: 36px !important;
    height: 36px !important;
  }

  .publish-btn-improved {
    height: 36px !important;
    min-width: 80px;
    font-size: 0.8rem;
  }
}
</style>

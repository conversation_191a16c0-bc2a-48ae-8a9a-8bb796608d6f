import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Submission } from 'src/types/models';

export class SummaryService {
  private path = '/submissions';

  async getByAssessmentId(assessmentId: number): Promise<Submission> {
    try {
      const res = await api.get<Submission>(`${this.path}/${assessmentId}`);
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'ดึงข้อมูลสรุปล้มเหลว' });
      throw new Error('Get summary by assessmentId failed');
    }
  }

  async getAll(): Promise<Submission[]> {
    try {
      const res = await api.get<Submission[]>(this.path);
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'ดึงข้อมูลสรุปทั้งหมดล้มเหลว' });
      throw new Error('Get all summaries failed');
    }
  }

  async create(data: Submission): Promise<Submission> {
    try {
      const res = await api.post<Submission>(this.path, data);
      Notify.create({ type: 'positive', message: 'สร้างรายงานสรุปสำเร็จ' });
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'สร้างรายงานสรุปล้มเหลว' });
      throw new Error('Create summary failed');
    }
  }

  async update(id: number, data: Submission): Promise<Submission> {
    try {
      const res = await api.patch<Submission>(`${this.path}/${id}`, data);
      Notify.create({ type: 'positive', message: 'อัปเดตรายงานสรุปสำเร็จ' });
      return res.data;
    } catch {
      Notify.create({ type: 'negative', message: 'อัปเดตรายงานสรุปล้มเหลว' });
      throw new Error('Update summary failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
      Notify.create({ type: 'positive', message: 'ลบรายงานสรุปสำเร็จ' });
    } catch {
      Notify.create({ type: 'negative', message: 'ลบรายงานสรุปล้มเหลว' });
      throw new Error('Remove summary failed');
    }
  }
}

export const summaryService = new SummaryService();

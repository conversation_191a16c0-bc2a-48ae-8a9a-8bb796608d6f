import type { RouteRecordRaw } from 'vue-router';

const quizRoutes: RouteRecordRaw[] = [
  {
    path: '/quiz',
    name: 'quiz',
    component: () => import('../../layouts/AsmLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/quiz') {
        next({ name: 'quiz-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'quiz-management',
        component: () => import('../../pages/quiz/QuizManagementPage.vue'),
        meta: {},
      },
      {
        path: ':id(\\d+)',
        name: 'quiz-id',
        component: () => import('../../pages/quiz/QuizEditorPage.vue'),
        // beforeEnter: (to, from, next) => {
        //   // * auth check
        // },
        props: true,
      },
      {
        path: 'do-quiz/:id(\\d+):questionId(\\d+)?',
        name: 'do-quiz',
        component: () => import('../../pages/quiz/QuizPageId.vue'),
        props: true,
      },
    ],
  },
];

export default quizRoutes;

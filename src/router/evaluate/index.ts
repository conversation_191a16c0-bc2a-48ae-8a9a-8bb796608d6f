import type { RouteRecordRaw } from 'vue-router';

const evaluateRoutes: RouteRecordRaw[] = [
  {
    path: '/evaluate',
    name: 'evaluate',
    component: () => import('../../layouts/AsmLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/evaluate') {
        next({ name: 'evaluate-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'evaluate-management',
        component: () => import('../../pages/evaluate/EvaluateManagementPage.vue'),
        meta: {},
      },
      {
        path: ':id(\\d+)',
        name: 'evaluate-id',
        component: () => import('../../pages/evaluate/EvaluateEditorPage.vue'),
        props: true,
      },
      {
        path: 'user/do-evaluate',
        component: () => import('src/pages/evaluate/EvaluateIdPage.vue'),
        meta: {
          perms: [1, 2],
        },
        props: (route) => ({ id: route.query.id }),
      },
    ],
  },
];

export default evaluateRoutes;

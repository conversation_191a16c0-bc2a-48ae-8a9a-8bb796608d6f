import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useCreateItemEvaluateFormStore = defineStore('createEvaluateForm', () => {
  //state

  //choice
  const radioChoices = ref<{ placeholder: string; value: string }[]>([
    { placeholder: 'ตัวเลือกที่ 1', value: '' },
  ]);
  const choiceSelectedOption = ref<string[]>([]);
  //checkbox
  const checkboxChoices = ref<{ placeholder: string; value: string }[]>([
    { placeholder: 'ตัวเลือกที่ 1', value: '' },
  ]);
  const checkboxSelectedOptions = ref<string[]>([]);
  //grid
  const gridRowQuestions = ref<{ label: string; value: string }[]>([
    { label: 'คำถามที่ 1', value: 'question1' },
  ]);
  const gridColumnChoices = ref<{ label: string; value: string }[]>([
    { label: 'ตัวเลือกที่ 1', value: 'option1' },
  ]);
  //text
  const textInput = ref('');
  const draggedIndex = ref<number | null>(null);
  const draggedSection = ref<'row' | 'col' | null>(null);
  const hoveredIndex = ref<number | null>(null);

  // Actions
  const addChoice = (isCheckbox: boolean = false) => {
    const choices = isCheckbox ? checkboxChoices.value : radioChoices.value;
    const newIndex = choices.length + 1;
    choices.push({ placeholder: `ตัวเลือกที่ ${newIndex}`, value: `` });
  };

  const addOtherChoice = (isCheckbox: boolean = false) => {
    const choices = isCheckbox ? checkboxChoices.value : radioChoices.value;
    choices.push({ placeholder: 'อื่นๆ', value: `` });
  };

  const updateChoice = (index: number, isCheckbox: boolean = false) => {
    const choices = isCheckbox ? checkboxChoices.value : radioChoices.value;
    choices[index]!.value =
      choices[index]!.placeholder === 'อื่นๆ' ? `other${index + 1}` : `option${index + 1}`;
  };

  const removeChoice = (index: number, isCheckbox: boolean = false) => {
    const choices = isCheckbox ? checkboxChoices.value : radioChoices.value;
    let selectedOptions = isCheckbox ? checkboxSelectedOptions.value : choiceSelectedOption.value;
    if (choices.length > 1) {
      choices.splice(index, 1);
      const resetChoices = choices.map((choice) => ({
        ...choice,
        value: ``,
      }));
      if (isCheckbox) {
        checkboxChoices.value = resetChoices;
      } else {
        radioChoices.value = resetChoices;
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      selectedOptions = selectedOptions.filter((val) =>
        choices.some((choice) => choice.value === val),
      );
    }
  };

  //grid
  const addRowQuestion = () => {
    const newIndex = gridRowQuestions.value.length + 1;
    gridRowQuestions.value.push({ label: `คำถามที่ ${newIndex}`, value: `question${newIndex}` });
  };

  const addColumnChoice = () => {
    const newIndex = gridColumnChoices.value.length + 1;
    gridColumnChoices.value.push({ label: `ตัวเลือกที่ ${newIndex}`, value: `option${newIndex}` });
  };

  const removeRowQuestion = (index: number) => {
    if (gridRowQuestions.value.length > 1) {
      gridRowQuestions.value.splice(index, 1);
      gridRowQuestions.value = gridRowQuestions.value.map((question, idx) => ({
        ...question,
        value: `question${idx + 1}`,
      }));
    }
  };

  const removeColumnChoice = (index: number) => {
    if (gridColumnChoices.value.length > 1) {
      gridColumnChoices.value.splice(index, 1);
      gridColumnChoices.value = gridColumnChoices.value.map((choice, idx) => ({
        ...choice,
        value: `option${idx + 1}`,
      }));
    }
  };

  const updateRowQuestion = (index: number) => {
    gridRowQuestions.value[index]!.value = `question${index + 1}`;
  };

  const updateColumnChoice = (index: number) => {
    gridColumnChoices.value[index]!.value = `option${index + 1}`;
  };

  //drag func
  const startDrag = (index: number, section?: 'row' | 'col') => {
    draggedIndex.value = index;
    if (section) draggedSection.value = section;
  };

  const handleDragStart = (event: DragEvent) => {
    if (draggedIndex.value !== null && (draggedSection.value || draggedSection.value === null)) {
      event.dataTransfer!.setData(
        'text/plain',
        draggedSection.value
          ? `${draggedSection.value}-${draggedIndex.value}`
          : draggedIndex.value.toString(),
      );
      event.dataTransfer!.effectAllowed = 'move';
      event.dataTransfer!.dropEffect = 'move';
    }
  };

  const hoverRow = (index: number) => {
    hoveredIndex.value = index;
  };

  const drop = (index: number, event: DragEvent, isCheckbox: boolean = false) => {
    event.preventDefault();
    if (draggedIndex.value === null || draggedIndex.value === index) return;
    const choices = isCheckbox ? checkboxChoices.value : radioChoices.value;
    const draggedChoice: { placeholder: string; value: string } = {
      placeholder: choices[draggedIndex.value]!.placeholder,
      value: choices[draggedIndex.value]!.value,
    };
    choices.splice(draggedIndex.value, 1);
    choices.splice(index, 0, draggedChoice);
    choices.forEach((choice, idx) => {
      choice.value = choice.placeholder === 'อื่นๆ' ? `other${idx + 1}` : `option${idx + 1}`;
    });
    draggedIndex.value = null;
    hoveredIndex.value = null;
  };

  const gridDrop = (index: number, event: DragEvent, section: 'row' | 'col') => {
    event.preventDefault();
    if (
      draggedIndex.value === null ||
      draggedSection.value !== section ||
      draggedIndex.value === index
    )
      return;

    const sourceList = section === 'row' ? gridRowQuestions.value : gridColumnChoices.value;
    const targetList = section === 'row' ? gridRowQuestions.value : gridColumnChoices.value;
    const draggedItem = {
      label: sourceList[draggedIndex.value]!.label,
      value: sourceList[draggedIndex.value]!.value,
    };
    sourceList.splice(draggedIndex.value, 1);
    targetList.splice(index, 0, draggedItem);

    if (section === 'row') {
      gridRowQuestions.value = gridRowQuestions.value.map((question, idx) => ({
        ...question,
        value: `question${idx + 1}`,
      }));
    } else {
      gridColumnChoices.value = gridColumnChoices.value.map((choice, idx) => ({
        ...choice,
        value: `option${idx + 1}`,
      }));
    }

    draggedIndex.value = null;
    draggedSection.value = null;
    hoveredIndex.value = null;
  };

  const endDrag = () => {
    draggedIndex.value = null;
    draggedSection.value = null;
    hoveredIndex.value = null;
  };

  return {
    //state
    radioChoices,
    choiceSelectedOption,
    checkboxChoices,
    checkboxSelectedOptions,
    gridRowQuestions,
    gridColumnChoices,
    textInput,
    draggedIndex,
    draggedSection,
    hoveredIndex,

    // Actions
    updateChoice,
    addChoice,
    addOtherChoice,
    removeChoice,
    addRowQuestion,
    addColumnChoice,
    removeRowQuestion,
    removeColumnChoice,
    updateRowQuestion,
    updateColumnChoice,
    startDrag,
    handleDragStart,
    hoverRow,
    drop,
    gridDrop,
    endDrag,
  };
});
